1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anginatech.textrepeater"
4    android:versionCode="3"
5    android:versionName="3.9.1" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:5-76
12-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:22-73
13    <uses-permission android:name="android.permission.WAKE_LOCK" />
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:5-66
14-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:5-78
15-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:5-82
16-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:22-79
17
18    <!-- Notification permission for Android 13+ (API 33+) -->
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:5-77
19-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:22-74
20
21    <supports-screens
21-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:15:5-18:11
22        android:anyDensity="true"
22-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:16:9-34
23        android:resizeable="true" />
23-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:17:9-34
24
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
25-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:22-76
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
26-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
27    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
27-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
28    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
28-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
29    <queries>
29-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
30
31        <!-- For browser content -->
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent>
39        <!-- End of browser content -->
40        <!-- For CustomTabsService -->
41        <intent>
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
42            <action android:name="android.support.customtabs.action.CustomTabsService" />
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
42-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
43        </intent>
44        <!-- End of CustomTabsService -->
45        <!-- For MRAID capabilities -->
46        <intent>
46-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
47            <action android:name="android.intent.action.INSERT" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
48
49            <data android:mimeType="vnd.android.cursor.dir/event" />
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
50        </intent>
51        <intent>
51-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
52            <action android:name="android.intent.action.VIEW" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
53
54            <data android:scheme="sms" />
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
55        </intent>
56        <intent>
56-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
57            <action android:name="android.intent.action.DIAL" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
58
59            <data android:path="tel:" />
59-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
60        </intent>
61        <!-- End of MRAID capabilities -->
62    </queries>
63
64    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
64-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
65    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
65-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
66
67    <permission
67-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
68        android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
68-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
69        android:protectionLevel="signature" />
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
70
71    <uses-permission android:name="com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
72
73    <application
73-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:20:5-149:19
74        android:name="com.anginatech.textrepeater.MyApplication"
74-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:21:9-38
75        android:allowBackup="true"
75-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:22:9-35
76        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
76-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
77        android:dataExtractionRules="@xml/data_extraction_rules"
77-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:23:9-65
78        android:debuggable="true"
79        android:extractNativeLibs="false"
80        android:fullBackupContent="@xml/backup_rules"
80-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:24:9-54
81        android:icon="@mipmap/ic_launcher"
81-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:25:9-43
82        android:label="@string/app_name"
82-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:26:9-41
83        android:roundIcon="@mipmap/ic_launcher_round"
83-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:27:9-54
84        android:supportsRtl="true"
84-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:28:9-35
85        android:testOnly="true"
86        android:theme="@style/Theme.TextRepeater"
86-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:29:9-50
87        android:usesCleartextTraffic="true" >
87-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:30:9-44
88        <activity
88-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:32:9-37:15
89            android:name="com.anginatech.textrepeater.Decoration_Text_Activity"
89-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:33:13-53
90            android:configChanges="uiMode|screenSize|orientation"
90-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:35:13-66
91            android:exported="false"
91-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:34:13-37
92            android:windowSoftInputMode="adjustPan" />
92-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:36:13-52
93        <activity
93-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:38:9-43:15
94            android:name="com.anginatech.textrepeater.Stylish_Font_Activity"
94-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:39:13-50
95            android:configChanges="uiMode|screenSize|orientation"
95-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:40:13-66
96            android:exported="false"
96-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:41:13-37
97            android:windowSoftInputMode="adjustPan" />
97-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:42:13-52
98        <activity
98-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:44:9-49:15
99            android:name="com.anginatech.textrepeater.Text_to_Imoji_Activity"
99-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:45:13-51
100            android:configChanges="uiMode|screenSize|orientation"
100-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:46:13-66
101            android:exported="false"
101-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:47:13-37
102            android:windowSoftInputMode="adjustPan" />
102-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:48:13-52
103        <activity
103-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:50:9-55:15
104            android:name="com.anginatech.textrepeater.Blank_Text_Activity"
104-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:51:13-48
105            android:configChanges="uiMode|screenSize|orientation"
105-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:52:13-66
106            android:exported="false"
106-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:53:13-37
107            android:windowSoftInputMode="adjustPan" />
107-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:54:13-52
108        <activity
108-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:56:9-61:15
109            android:name="com.anginatech.textrepeater.Emoji_Art"
109-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:57:13-38
110            android:configChanges="uiMode|screenSize|orientation"
110-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:58:13-66
111            android:exported="false"
111-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:59:13-37
112            android:windowSoftInputMode="adjustPan" />
112-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:60:13-52
113        <activity
113-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:62:9-67:15
114            android:name="com.anginatech.textrepeater.Text_Repeat"
114-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:63:13-40
115            android:configChanges="uiMode|screenSize|orientation"
115-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:64:13-66
116            android:exported="false"
116-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:65:13-37
117            android:windowSoftInputMode="adjustPan" />
117-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:66:13-52
118        <activity
118-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:68:9-73:15
119            android:name="com.anginatech.textrepeater.Random_Text_Activity"
119-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:69:13-49
120            android:configChanges="uiMode|screenSize|orientation"
120-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:70:13-66
121            android:exported="false"
121-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:71:13-37
122            android:windowSoftInputMode="adjustPan" />
122-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:72:13-52
123        <activity
123-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:74:9-79:15
124            android:name="com.anginatech.textrepeater.Message_Activity"
124-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:75:13-45
125            android:configChanges="uiMode|screenSize|orientation"
125-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:76:13-66
126            android:exported="false"
126-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:77:13-37
127            android:windowSoftInputMode="adjustPan" />
127-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:78:13-52
128        <activity
128-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:80:9-85:15
129            android:name="com.anginatech.textrepeater.Settings_Activity"
129-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:81:13-46
130            android:configChanges="uiMode|screenSize|orientation"
130-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:82:13-66
131            android:exported="false"
131-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:83:13-37
132            android:windowSoftInputMode="adjustPan" />
132-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:84:13-52
133        <activity
133-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:86:9-91:15
134            android:name="com.anginatech.textrepeater.Navigation_Activity"
134-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:87:13-48
135            android:configChanges="uiMode|screenSize|orientation"
135-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:88:13-66
136            android:exported="false"
136-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:89:13-37
137            android:windowSoftInputMode="adjustPan" />
137-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:90:13-52
138        <activity
138-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:92:9-103:20
139            android:name="com.anginatech.textrepeater.Splash_Screen"
139-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:93:13-42
140            android:configChanges="uiMode|screenSize|orientation"
140-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:94:13-66
141            android:exported="true"
141-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:95:13-36
142            android:windowSoftInputMode="adjustPan" >
142-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:96:13-52
143            <intent-filter>
143-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:98:13-102:29
144                <action android:name="android.intent.action.MAIN" />
144-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:17-69
144-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:25-66
145
146                <category android:name="android.intent.category.LAUNCHER" />
146-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:17-77
146-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:27-74
147            </intent-filter>
148        </activity>
149        <activity
149-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:104:9-109:15
150            android:name="com.anginatech.textrepeater.MainActivity"
150-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:105:13-41
151            android:configChanges="uiMode|screenSize|orientation"
151-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:106:13-66
152            android:exported="true"
152-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:107:13-36
153            android:windowSoftInputMode="adjustPan" />
153-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:108:13-52
154
155        <meta-data
155-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:112:9-114:57
156            android:name="preloaded_fonts"
156-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:113:13-43
157            android:resource="@array/preloaded_fonts" />
157-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:114:13-54
158        <meta-data
158-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:115:9-117:51
159            android:name="com.google.android.gms.ads.APPLICATION_ID"
159-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:116:13-69
160            android:value="@string/ADMOB_APP_ID" />
160-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:117:13-49
161
162        <property
162-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:119:9-123:15
163            android:name="android.adservices.AD_SERVICES_CONFIG"
163-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:120:13-65
164            android:resource="@xml/gma_ad_services_config" />
164-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:121:13-59
165
166        <!-- Firebase Cloud Messaging Service -->
167        <service
167-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:126:9-132:19
168            android:name="com.anginatech.textrepeater.MyFirebaseMessagingService"
168-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:127:13-55
169            android:exported="false" >
169-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:128:13-37
170            <intent-filter>
170-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
171                <action android:name="com.google.firebase.MESSAGING_EVENT" />
171-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
171-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
172            </intent-filter>
173        </service>
174
175        <!-- Firebase Cloud Messaging default notification icon -->
176        <meta-data
176-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:135:9-137:67
177            android:name="com.google.firebase.messaging.default_notification_icon"
177-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:136:13-83
178            android:resource="@drawable/ic_launcher_foreground" />
178-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:137:13-64
179
180        <!-- Firebase Cloud Messaging default notification color -->
181        <meta-data
181-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:140:9-142:46
182            android:name="com.google.firebase.messaging.default_notification_color"
182-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:141:13-84
183            android:resource="@color/love" />
183-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:142:13-43
184
185        <!-- Firebase Cloud Messaging default notification channel -->
186        <meta-data
186-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:145:9-147:59
187            android:name="com.google.firebase.messaging.default_notification_channel_id"
187-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:146:13-89
188            android:value="text_repeater_notifications" />
188-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:147:13-56
189
190        <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
191        <activity
191-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
192            android:name="com.google.android.gms.ads.AdActivity"
192-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
193            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
193-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
194            android:exported="false"
194-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
195            android:theme="@android:style/Theme.Translucent" />
195-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
196
197        <provider
197-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
198            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
198-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
199            android:authorities="com.anginatech.textrepeater.mobileadsinitprovider"
199-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
200            android:exported="false"
200-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
201            android:initOrder="100" />
201-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
202
203        <service
203-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
204            android:name="com.google.android.gms.ads.AdService"
204-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
205            android:enabled="true"
205-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
206            android:exported="false" />
206-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
207
208        <activity
208-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
209            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
209-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
210            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
210-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
211            android:exported="false" />
211-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
212        <activity
212-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
213            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
213-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
214            android:excludeFromRecents="true"
214-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
215            android:exported="false"
215-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
216            android:launchMode="singleTask"
216-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
217            android:taskAffinity=""
217-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
218            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
218-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
219
220        <meta-data
220-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
221            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
221-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
222            android:value="true" />
222-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
223        <meta-data
223-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
224            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
224-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
225            android:value="true" />
225-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
226
227        <receiver
227-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
228            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
228-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
229            android:exported="true"
229-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
230            android:permission="com.google.android.c2dm.permission.SEND" >
230-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
231            <intent-filter>
231-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
232                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
232-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
232-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
233            </intent-filter>
234
235            <meta-data
235-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
236                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
236-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
237                android:value="true" />
237-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
238        </receiver>
239        <!--
240             FirebaseMessagingService performs security checks at runtime,
241             but set to not exported to explicitly avoid allowing another app to call it.
242        -->
243        <service
243-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
244            android:name="com.google.firebase.messaging.FirebaseMessagingService"
244-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
245            android:directBootAware="true"
245-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
246            android:exported="false" >
246-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
247            <intent-filter android:priority="-500" >
247-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
248                <action android:name="com.google.firebase.MESSAGING_EVENT" />
248-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
248-->C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
249            </intent-filter>
250        </service>
251        <service
251-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
252            android:name="com.google.firebase.components.ComponentDiscoveryService"
252-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
253            android:directBootAware="true"
253-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
254            android:exported="false" >
254-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
255            <meta-data
255-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
256                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
256-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
257                android:value="com.google.firebase.components.ComponentRegistrar" />
257-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
258            <meta-data
258-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
259                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
259-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
260                android:value="com.google.firebase.components.ComponentRegistrar" />
260-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
261            <meta-data
261-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
262                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
262-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
264            <meta-data
264-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
265                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
265-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
267            <meta-data
267-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
268                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
268-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
269                android:value="com.google.firebase.components.ComponentRegistrar" />
269-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
270            <meta-data
270-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
271                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
271-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
272                android:value="com.google.firebase.components.ComponentRegistrar" />
272-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
273            <meta-data
273-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
274                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
275                android:value="com.google.firebase.components.ComponentRegistrar" />
275-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
276            <meta-data
276-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
277                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
277-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
278                android:value="com.google.firebase.components.ComponentRegistrar" />
278-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
279        </service>
280
281        <provider
281-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
282            android:name="com.google.firebase.provider.FirebaseInitProvider"
282-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
283            android:authorities="com.anginatech.textrepeater.firebaseinitprovider"
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
284            android:directBootAware="true"
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
285            android:exported="false"
285-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
286            android:initOrder="100" />
286-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
287
288        <receiver
288-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
289            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
289-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
290            android:enabled="true"
290-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
291            android:exported="false" >
291-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
292        </receiver>
293
294        <service
294-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
295            android:name="com.google.android.gms.measurement.AppMeasurementService"
295-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
296            android:enabled="true"
296-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
297            android:exported="false" />
297-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
298        <service
298-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
299            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
299-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
300            android:enabled="true"
300-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
301            android:exported="false"
301-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
302            android:permission="android.permission.BIND_JOB_SERVICE" />
302-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
303        <service
303-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
304            android:name="androidx.room.MultiInstanceInvalidationService"
304-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
305            android:directBootAware="true"
305-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
306            android:exported="false" />
306-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
307
308        <provider
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
309            android:name="androidx.startup.InitializationProvider"
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
310            android:authorities="com.anginatech.textrepeater.androidx-startup"
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
311            android:exported="false" >
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
312            <meta-data
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
313                android:name="androidx.work.WorkManagerInitializer"
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
314                android:value="androidx.startup" />
314-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
315            <meta-data
315-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
316                android:name="androidx.emoji2.text.EmojiCompatInitializer"
316-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
317                android:value="androidx.startup" />
317-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
318            <meta-data
318-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
319                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
319-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
320                android:value="androidx.startup" />
320-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
321            <meta-data
321-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
322                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
322-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
323                android:value="androidx.startup" />
323-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
324        </provider>
325
326        <service
326-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
327            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
327-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
328            android:directBootAware="false"
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
329            android:enabled="@bool/enable_system_alarm_service_default"
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
330            android:exported="false" />
330-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
331        <service
331-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
332            android:name="androidx.work.impl.background.systemjob.SystemJobService"
332-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
333            android:directBootAware="false"
333-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
334            android:enabled="@bool/enable_system_job_service_default"
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
335            android:exported="true"
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
336            android:permission="android.permission.BIND_JOB_SERVICE" />
336-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
337        <service
337-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
338            android:name="androidx.work.impl.foreground.SystemForegroundService"
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
340            android:enabled="@bool/enable_system_foreground_service_default"
340-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
341            android:exported="false" />
341-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
342
343        <receiver
343-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
344            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
344-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
345            android:directBootAware="false"
345-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
346            android:enabled="true"
346-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
347            android:exported="false" />
347-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
348        <receiver
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
349            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
349-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
350            android:directBootAware="false"
350-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
351            android:enabled="false"
351-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
352            android:exported="false" >
352-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
353            <intent-filter>
353-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
354                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
355                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
356            </intent-filter>
357        </receiver>
358        <receiver
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
359            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
360            android:directBootAware="false"
360-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
361            android:enabled="false"
361-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
362            android:exported="false" >
362-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
363            <intent-filter>
363-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
364                <action android:name="android.intent.action.BATTERY_OKAY" />
364-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
364-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
365                <action android:name="android.intent.action.BATTERY_LOW" />
365-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
365-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
366            </intent-filter>
367        </receiver>
368        <receiver
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
369            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
369-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
370            android:directBootAware="false"
370-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
371            android:enabled="false"
371-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
372            android:exported="false" >
372-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
373            <intent-filter>
373-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
374                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
374-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
374-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
375                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
375-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
375-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
376            </intent-filter>
377        </receiver>
378        <receiver
378-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
379            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
379-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
380            android:directBootAware="false"
380-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
381            android:enabled="false"
381-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
382            android:exported="false" >
382-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
383            <intent-filter>
383-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
384                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
384-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
384-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
385            </intent-filter>
386        </receiver>
387        <receiver
387-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
388            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
388-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
390            android:enabled="false"
390-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
391            android:exported="false" >
391-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
392            <intent-filter>
392-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
393                <action android:name="android.intent.action.BOOT_COMPLETED" />
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
393-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
394                <action android:name="android.intent.action.TIME_SET" />
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
394-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
395                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
395-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
396            </intent-filter>
397        </receiver>
398        <receiver
398-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
399            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
399-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
400            android:directBootAware="false"
400-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
401            android:enabled="@bool/enable_system_alarm_service_default"
401-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
402            android:exported="false" >
402-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
403            <intent-filter>
403-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
404                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
404-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
405            </intent-filter>
406        </receiver>
407        <receiver
407-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
408            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
408-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
409            android:directBootAware="false"
409-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
410            android:enabled="true"
410-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
411            android:exported="true"
411-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
412            android:permission="android.permission.DUMP" >
412-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
413            <intent-filter>
413-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
414                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
414-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
415            </intent-filter>
416        </receiver>
417
418        <activity
418-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
419            android:name="com.google.android.gms.common.api.GoogleApiActivity"
419-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
420            android:exported="false"
420-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
421            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
421-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
422
423        <uses-library
423-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
424            android:name="android.ext.adservices"
424-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
425            android:required="false" />
425-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
426
427        <meta-data
427-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
428            android:name="com.google.android.gms.version"
428-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
429            android:value="@integer/google_play_services_version" />
429-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
430
431        <service
431-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
432            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
432-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
433            android:exported="false" >
433-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
434            <meta-data
434-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
435                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
435-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
436                android:value="cct" />
436-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
437        </service>
438        <service
438-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
439            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
439-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
440            android:exported="false"
440-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
441            android:permission="android.permission.BIND_JOB_SERVICE" >
441-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
442        </service>
443
444        <receiver
444-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
445            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
445-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
446            android:exported="false" />
446-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
447        <receiver
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
448            android:name="androidx.profileinstaller.ProfileInstallReceiver"
448-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
449            android:directBootAware="false"
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
450            android:enabled="true"
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
451            android:exported="true"
451-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
452            android:permission="android.permission.DUMP" >
452-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
453            <intent-filter>
453-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
454                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
454-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
455            </intent-filter>
456            <intent-filter>
456-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
457                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
457-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
458            </intent-filter>
459            <intent-filter>
459-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
460                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
460-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
461            </intent-filter>
462            <intent-filter>
462-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
463                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
463-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
464            </intent-filter>
465        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
466        <activity
466-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
467            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
467-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
468            android:exported="false"
468-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
469            android:stateNotNeeded="true"
469-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
470            android:theme="@style/Theme.PlayCore.Transparent" />
470-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
471    </application>
472
473</manifest>
