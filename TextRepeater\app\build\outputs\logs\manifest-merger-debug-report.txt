-- Merging decision tree log ---
manifest
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:2:1-151:12
INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:2:1-151:12
INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:2:1-151:12
INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:2:1-151:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52784fb4cab316940f91ea2804bc5f24\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8413cc544fb6f2294367babd08869dc0\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76cfb25da81b2b5e2aa34d5596c62a3\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06874cb569871263837a9b5fbcc9e1ad\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb3ed86f488888ddc36a3563dc7e9ca1\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31a101b1dd1768464143814734cd85ae\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d416df2b40824e07b0e868cc5798bd03\transformed\play-services-ads-24.3.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:17:1-115:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b6ba5b3d360f014346b7b4fb1ffd2ff\transformed\user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcf222e282cdf8ecbd0d415beffa523\transformed\app-update-2.1.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cef60ab055715f09a031a08230dcfc2d\transformed\play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2f8fb98763bcb445b921d731094b8c\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4553b7b0322ab293bcc329136e2932d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4450b52078df85291c6d262f03cc57bf\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8ce38a76b0152adb07da0dc6b174bad\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\100491b5f701f818c3555f95fffcc986\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.paging:paging-runtime:3.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c25ea95ea23717cf9f0a60d9e651e74\transformed\paging-runtime-3.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e86129563e61bc22cb96223f46745b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e846c7ce75e46b00bb4e5d3e0ce7fe21\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fd9c3c38636d4f5ec42f8863d5fce42\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeb60ce14e1b35feb3713e4aeac38b43\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31838911ddf2b9e0272ac67c8256257b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88a770746f54397e9992fa635d190335\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb095984b8759cff21c4f7c421f13cd3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8da3bcb294627ef4e751386ac0635862\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d04c0754b631f5ed8d2cf5dcea99d863\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\14355531853b0307bd00cad5bbdd2fcd\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efbae47c6f6c47aca2470d3231a38361\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f32c36e6ca68ceb44de6177394d11f\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff6ddd3d4f795ca8cb7d4c2b6751ca9e\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\334295c5e04495636b703b2a748d1b37\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bb2f65be11e8ae124f395e710c246b9\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1e4aa6e5cf81534a6f491a5dbb08c6e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bc962e3f4e2214816970e0baea3a5fe\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b0100497282dee17c75c3ece58c3b1\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c613623d05009581d814a8a4084821d7\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e75b8dff086dfa465aa3b833269679c1\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e31f7c8c2c5cf64c78151d04c8f0c9c\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c38cfbe3e33e1ecfa512b9ab446db77\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\677e01fe423d3cc2ba2fb1afa3d31270\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c61ec164e471d2c2469ee4856a44c64\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29bed833840c7d0a6dd101506f437116\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\73144a929782b2343490e8c285243832\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\829ba952dbb51a754acc471c58ad5154\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6817713cba4393be5b4202833497147d\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd6af5ca358ce9eafd23650607dc7d4a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9876c998b55afad6da78fec2a590e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d979ba64cee439b72d4773ca6d43be0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3264a73f7fb28e511bca211b9e18ef2c\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0357035e4a4a51006e2ed675c7ca1da\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\02cc7eca9b4ffee5397fd81bf8d5802d\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58d06621dfd0343ac9056dc25db7644a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ca474bf66022d6cbdaf5c5bdcce7353\transformed\shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb4e97d4080595f01a0f7b19cf19df7\transformed\volley-1.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a57914fd80edaae2b09d67a6b912cbc\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb463db3f14b230af059202887f2195b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\90da34c2883df2039a77b0bed44cee35\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c98637fe55aeb5741cb66391d469e61b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bc22fd557b3a6989213b909ddf57de9\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b58f21c35a838985ed4b6ee9ff075017\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f0d586566dcb5c8cbd26ad25dc6b203\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72aed868dec08c94ceff9820efddb103\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40838462b366764db4095cc64f5d44\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ab960c895e681e0067876d243789774\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3b2c631f1a8676bff63761d3e74bfbc\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed7097c05d7da48013a80d1fef7ca91\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44680558f7a6f8e32be3d9c978031089\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\035038a2743ff0765f68cbe27794291b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\257870fcf4c56046cb9a7cb05de3e7a2\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd13a4415d11d8e38e7319f00727c40a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9a7dded6bc8a71533797c9d81924f3e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecd46bd72d726720d7334d8192292a5b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fffad8bdc763252d884e55ed15bcff1a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:5-76
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:6:22-73
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:7:22-65
uses-permission#android.permission.VIBRATE
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:5-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:5-78
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d979ba64cee439b72d4773ca6d43be0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d979ba64cee439b72d4773ca6d43be0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:10:22-79
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:13:22-74
supports-screens
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:15:5-18:11
	android:resizeable
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:17:9-34
	android:anyDensity
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:16:9-34
application
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:20:5-149:19
INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:20:5-149:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52784fb4cab316940f91ea2804bc5f24\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52784fb4cab316940f91ea2804bc5f24\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8413cc544fb6f2294367babd08869dc0\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8413cc544fb6f2294367babd08869dc0\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcf222e282cdf8ecbd0d415beffa523\transformed\app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcf222e282cdf8ecbd0d415beffa523\transformed\app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cef60ab055715f09a031a08230dcfc2d\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cef60ab055715f09a031a08230dcfc2d\transformed\play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2f8fb98763bcb445b921d731094b8c\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2f8fb98763bcb445b921d731094b8c\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4553b7b0322ab293bcc329136e2932d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4553b7b0322ab293bcc329136e2932d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4450b52078df85291c6d262f03cc57bf\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4450b52078df85291c6d262f03cc57bf\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:29:5-30:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff6ddd3d4f795ca8cb7d4c2b6751ca9e\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff6ddd3d4f795ca8cb7d4c2b6751ca9e\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9876c998b55afad6da78fec2a590e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9876c998b55afad6da78fec2a590e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d979ba64cee439b72d4773ca6d43be0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d979ba64cee439b72d4773ca6d43be0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3264a73f7fb28e511bca211b9e18ef2c\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3264a73f7fb28e511bca211b9e18ef2c\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0357035e4a4a51006e2ed675c7ca1da\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0357035e4a4a51006e2ed675c7ca1da\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c98637fe55aeb5741cb66391d469e61b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c98637fe55aeb5741cb66391d469e61b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\035038a2743ff0765f68cbe27794291b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\035038a2743ff0765f68cbe27794291b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:28:9-35
	android:label
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:26:9-41
	android:fullBackupContent
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:24:9-54
	android:roundIcon
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:27:9-54
	tools:targetApi
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:31:9-29
	android:icon
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:25:9-43
	android:allowBackup
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:22:9-35
	android:theme
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:29:9-50
	android:dataExtractionRules
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:23:9-65
	android:usesCleartextTraffic
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:30:9-44
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:21:9-38
activity#com.anginatech.textrepeater.Decoration_Text_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:32:9-37:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:36:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:34:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:35:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:33:13-53
activity#com.anginatech.textrepeater.Stylish_Font_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:38:9-43:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:42:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:41:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:40:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:39:13-50
activity#com.anginatech.textrepeater.Text_to_Imoji_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:44:9-49:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:48:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:47:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:46:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:45:13-51
activity#com.anginatech.textrepeater.Blank_Text_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:50:9-55:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:54:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:53:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:52:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:51:13-48
activity#com.anginatech.textrepeater.Emoji_Art
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:56:9-61:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:60:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:59:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:58:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:57:13-38
activity#com.anginatech.textrepeater.Text_Repeat
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:62:9-67:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:66:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:65:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:64:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:63:13-40
activity#com.anginatech.textrepeater.Random_Text_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:68:9-73:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:72:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:71:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:70:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:69:13-49
activity#com.anginatech.textrepeater.Message_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:74:9-79:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:78:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:77:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:76:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:75:13-45
activity#com.anginatech.textrepeater.Settings_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:80:9-85:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:84:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:83:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:82:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:81:13-46
activity#com.anginatech.textrepeater.Navigation_Activity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:86:9-91:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:90:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:89:13-37
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:88:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:87:13-48
activity#com.anginatech.textrepeater.Splash_Screen
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:92:9-103:20
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:96:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:95:13-36
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:94:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:93:13-42
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:98:13-102:29
action#android.intent.action.MAIN
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:17-69
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:99:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:17-77
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:101:27-74
activity#com.anginatech.textrepeater.MainActivity
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:104:9-109:15
	android:windowSoftInputMode
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:108:13-52
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:107:13-36
	android:configChanges
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:106:13-66
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:105:13-41
meta-data#preloaded_fonts
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:112:9-114:57
	android:resource
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:114:13-54
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:113:13-43
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:115:9-117:51
	android:value
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:117:13-49
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:116:13-69
property#android.adservices.AD_SERVICES_CONFIG
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:119:9-123:15
	android:resource
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:121:13-59
	tools:replace
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:122:13-45
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:120:13-65
service#com.anginatech.textrepeater.MyFirebaseMessagingService
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:126:9-132:19
	android:exported
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:128:13-37
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:127:13-55
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:129:13-131:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:17-78
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:130:25-75
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:135:9-137:67
	android:resource
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:137:13-64
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:136:13-83
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:140:9-142:46
	android:resource
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:142:13-43
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:141:13-84
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:145:9-147:59
	android:value
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:147:13-56
	android:name
		ADDED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml:146:13-89
uses-sdk
INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52784fb4cab316940f91ea2804bc5f24\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52784fb4cab316940f91ea2804bc5f24\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8413cc544fb6f2294367babd08869dc0\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8413cc544fb6f2294367babd08869dc0\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76cfb25da81b2b5e2aa34d5596c62a3\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c76cfb25da81b2b5e2aa34d5596c62a3\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06874cb569871263837a9b5fbcc9e1ad\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06874cb569871263837a9b5fbcc9e1ad\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb3ed86f488888ddc36a3563dc7e9ca1\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb3ed86f488888ddc36a3563dc7e9ca1\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31a101b1dd1768464143814734cd85ae\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31a101b1dd1768464143814734cd85ae\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d416df2b40824e07b0e868cc5798bd03\transformed\play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d416df2b40824e07b0e868cc5798bd03\transformed\play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b6ba5b3d360f014346b7b4fb1ffd2ff\transformed\user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5b6ba5b3d360f014346b7b4fb1ffd2ff\transformed\user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcf222e282cdf8ecbd0d415beffa523\transformed\app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ffcf222e282cdf8ecbd0d415beffa523\transformed\app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cef60ab055715f09a031a08230dcfc2d\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cef60ab055715f09a031a08230dcfc2d\transformed\play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2f8fb98763bcb445b921d731094b8c\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c2f8fb98763bcb445b921d731094b8c\transformed\firebase-analytics-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4553b7b0322ab293bcc329136e2932d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4553b7b0322ab293bcc329136e2932d\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4450b52078df85291c6d262f03cc57bf\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4450b52078df85291c6d262f03cc57bf\transformed\play-services-measurement-sdk-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8ce38a76b0152adb07da0dc6b174bad\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8ce38a76b0152adb07da0dc6b174bad\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\100491b5f701f818c3555f95fffcc986\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\100491b5f701f818c3555f95fffcc986\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.paging:paging-runtime:3.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c25ea95ea23717cf9f0a60d9e651e74\transformed\paging-runtime-3.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.paging:paging-runtime:3.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c25ea95ea23717cf9f0a60d9e651e74\transformed\paging-runtime-3.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e86129563e61bc22cb96223f46745b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42e86129563e61bc22cb96223f46745b\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e846c7ce75e46b00bb4e5d3e0ce7fe21\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e846c7ce75e46b00bb4e5d3e0ce7fe21\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fd9c3c38636d4f5ec42f8863d5fce42\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1fd9c3c38636d4f5ec42f8863d5fce42\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeb60ce14e1b35feb3713e4aeac38b43\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\eeb60ce14e1b35feb3713e4aeac38b43\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31838911ddf2b9e0272ac67c8256257b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\31838911ddf2b9e0272ac67c8256257b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88a770746f54397e9992fa635d190335\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\88a770746f54397e9992fa635d190335\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb095984b8759cff21c4f7c421f13cd3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb095984b8759cff21c4f7c421f13cd3\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8da3bcb294627ef4e751386ac0635862\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8da3bcb294627ef4e751386ac0635862\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d04c0754b631f5ed8d2cf5dcea99d863\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d04c0754b631f5ed8d2cf5dcea99d863\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\14355531853b0307bd00cad5bbdd2fcd\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\14355531853b0307bd00cad5bbdd2fcd\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efbae47c6f6c47aca2470d3231a38361\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efbae47c6f6c47aca2470d3231a38361\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f32c36e6ca68ceb44de6177394d11f\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2f32c36e6ca68ceb44de6177394d11f\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff6ddd3d4f795ca8cb7d4c2b6751ca9e\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff6ddd3d4f795ca8cb7d4c2b6751ca9e\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\334295c5e04495636b703b2a748d1b37\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\334295c5e04495636b703b2a748d1b37\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bb2f65be11e8ae124f395e710c246b9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bb2f65be11e8ae124f395e710c246b9\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1e4aa6e5cf81534a6f491a5dbb08c6e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1e4aa6e5cf81534a6f491a5dbb08c6e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bc962e3f4e2214816970e0baea3a5fe\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2bc962e3f4e2214816970e0baea3a5fe\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b0100497282dee17c75c3ece58c3b1\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b0100497282dee17c75c3ece58c3b1\transformed\ads-adservices-java-1.1.0-beta11\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c613623d05009581d814a8a4084821d7\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c613623d05009581d814a8a4084821d7\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e75b8dff086dfa465aa3b833269679c1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e75b8dff086dfa465aa3b833269679c1\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e31f7c8c2c5cf64c78151d04c8f0c9c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2e31f7c8c2c5cf64c78151d04c8f0c9c\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c38cfbe3e33e1ecfa512b9ab446db77\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c38cfbe3e33e1ecfa512b9ab446db77\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\677e01fe423d3cc2ba2fb1afa3d31270\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\677e01fe423d3cc2ba2fb1afa3d31270\transformed\lifecycle-livedata-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c61ec164e471d2c2469ee4856a44c64\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c61ec164e471d2c2469ee4856a44c64\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29bed833840c7d0a6dd101506f437116\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\29bed833840c7d0a6dd101506f437116\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\73144a929782b2343490e8c285243832\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\73144a929782b2343490e8c285243832\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\829ba952dbb51a754acc471c58ad5154\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\829ba952dbb51a754acc471c58ad5154\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6817713cba4393be5b4202833497147d\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6817713cba4393be5b4202833497147d\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd6af5ca358ce9eafd23650607dc7d4a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd6af5ca358ce9eafd23650607dc7d4a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9876c998b55afad6da78fec2a590e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d9876c998b55afad6da78fec2a590e0\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d979ba64cee439b72d4773ca6d43be0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d979ba64cee439b72d4773ca6d43be0\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3264a73f7fb28e511bca211b9e18ef2c\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3264a73f7fb28e511bca211b9e18ef2c\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0357035e4a4a51006e2ed675c7ca1da\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e0357035e4a4a51006e2ed675c7ca1da\transformed\play-services-measurement-base-22.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\02cc7eca9b4ffee5397fd81bf8d5802d\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\02cc7eca9b4ffee5397fd81bf8d5802d\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58d06621dfd0343ac9056dc25db7644a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\58d06621dfd0343ac9056dc25db7644a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ca474bf66022d6cbdaf5c5bdcce7353\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ca474bf66022d6cbdaf5c5bdcce7353\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb4e97d4080595f01a0f7b19cf19df7\transformed\volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [com.android.volley:volley:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bfb4e97d4080595f01a0f7b19cf19df7\transformed\volley-1.2.1\AndroidManifest.xml:5:5-43
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a57914fd80edaae2b09d67a6b912cbc\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a57914fd80edaae2b09d67a6b912cbc\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb463db3f14b230af059202887f2195b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb463db3f14b230af059202887f2195b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\90da34c2883df2039a77b0bed44cee35\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\90da34c2883df2039a77b0bed44cee35\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c98637fe55aeb5741cb66391d469e61b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c98637fe55aeb5741cb66391d469e61b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bc22fd557b3a6989213b909ddf57de9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8bc22fd557b3a6989213b909ddf57de9\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b58f21c35a838985ed4b6ee9ff075017\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b58f21c35a838985ed4b6ee9ff075017\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f0d586566dcb5c8cbd26ad25dc6b203\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f0d586566dcb5c8cbd26ad25dc6b203\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72aed868dec08c94ceff9820efddb103\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72aed868dec08c94ceff9820efddb103\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40838462b366764db4095cc64f5d44\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1d40838462b366764db4095cc64f5d44\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ab960c895e681e0067876d243789774\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ab960c895e681e0067876d243789774\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3b2c631f1a8676bff63761d3e74bfbc\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3b2c631f1a8676bff63761d3e74bfbc\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed7097c05d7da48013a80d1fef7ca91\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed7097c05d7da48013a80d1fef7ca91\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44680558f7a6f8e32be3d9c978031089\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44680558f7a6f8e32be3d9c978031089\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\035038a2743ff0765f68cbe27794291b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\035038a2743ff0765f68cbe27794291b\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\257870fcf4c56046cb9a7cb05de3e7a2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\257870fcf4c56046cb9a7cb05de3e7a2\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd13a4415d11d8e38e7319f00727c40a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd13a4415d11d8e38e7319f00727c40a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9a7dded6bc8a71533797c9d81924f3e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a9a7dded6bc8a71533797c9d81924f3e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecd46bd72d726720d7334d8192292a5b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecd46bd72d726720d7334d8192292a5b\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fffad8bdc763252d884e55ed15bcff1a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fffad8bdc763252d884e55ed15bcff1a\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d416df2b40824e07b0e868cc5798bd03\transformed\play-services-ads-24.3.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\021b19875c009535eacd5dcf8348164d\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66440bfbec9f4bda8571c79c71a7a287\transformed\play-services-measurement-sdk-api-22.4.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:31:9-65
queries
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
data
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
	android:scheme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ec48de595b5bc04f70ea35bb09040215\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\314b9d9ae2fcb3ffc08f1d0e27710e18\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eeba63e103283234d6c1ae9f5a9dd94\transformed\play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb1d71432dbc375ad461e6ddb147a843\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6d7c7852e5b6cadbd2795fb56606897\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4f6d1c3e559cfa8ebd3ec0dfabfb711d\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\27feddebe3548fef998bd075b8810ff5\transformed\play-services-measurement-impl-22.4.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92d5861f759ff160df961f9a8db76e57\transformed\play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\08d70ac363b977cda0b3ea22a0bf61d8\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c98637fe55aeb5741cb66391d469e61b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c98637fe55aeb5741cb66391d469e61b\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ecc2f8f3468abaadb11665410159d236\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\609c2d8fd2575b0b7929f7842c23f1ec\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\227931f7180d1c697410396cca90ca2c\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.13\transforms\a737844196b9796a34390ad4a89016dd\transformed\ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.anginatech.textrepeater.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b6c0c0ba5c0df348132c3cb36235b804\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4af6a9e1a2cd48a0b9c98e91216c139c\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e470b172b24b8cdb4e12a7a7e275f479\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84d75324670b5cf792576f0061661420\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7a108fea80323b4891ed6bffd20602\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\495eb0e648b6e2d8d85e836707ecb5d1\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2eb77285bbc4983f317ee25688da504\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\99c4a3f269bb9bbfecce53cc05d3ad83\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
