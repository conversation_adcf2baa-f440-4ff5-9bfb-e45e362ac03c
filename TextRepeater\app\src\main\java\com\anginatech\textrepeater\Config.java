package com.anginatech.textrepeater;

/**
 * Configuration class for Text Repeater App Contains all API endpoints, app
 * settings, and configuration constants
 */
public class Config {

    // API Configuration
    public static final String BASE_URL = "http://192.168.0.106/monirulvitextrepeater/TextRepeaterAdminPanel/app-api/";
    public static final String BASE_DOMAIN = "http://192.168.0.106/monirulvitextrepeater/TextRepeaterAdminPanel";
    public static final String LEGACY_API_URL = "http://192.168.0.106/monirulvitextrepeater/TextRepeaterAdminPanel/app-api/index.php";
    //public static final String BASE_URL = "https://nextkotha.com/TextRepeaterAdminPanel/app-api/";
    // public static final String BASE_DOMAIN = "https://nextkotha.com/TextRepeaterAdminPanel";
    // public static final String LEGACY_API_URL = "https://nextkotha.com/TextRepeaterAdminPanel/app-api/index.php";
    public static final String API_VERSION = "1.0.0";
    public static final int API_TIMEOUT = 30000; // 30 seconds
    public static final int API_RETRY_COUNT = 3;

    // API Endpoints
    public static final String ENDPOINT_HEALTH = "health";
    public static final String ENDPOINT_VERSION = "version";
    public static final String ENDPOINT_SETTINGS = "settings/settings";
    public static final String ENDPOINT_CONFIG = "settings/config";
    public static final String ENDPOINT_FEATURES = "settings/features";
    public static final String ENDPOINT_MAINTENANCE = "settings/maintenance";
    public static final String ENDPOINT_ADS_CONFIG = "ads/config";
    public static final String ENDPOINT_ADS_TRACK = "ads/track";
    public static final String ENDPOINT_USER_REGISTER = "users/register";
    public static final String ENDPOINT_USER_UPDATE = "users/update";
    public static final String ENDPOINT_NOTIFICATION_DELIVERED = "notifications/delivered";
    public static final String ENDPOINT_NOTIFICATION_CLICKED = "notifications/clicked";
    public static final String ENDPOINT_NOTIFICATION_REGISTER = "notifications/register";
    public static final String ENDPOINT_NOTIFICATION_HISTORY = "notifications/history";
    public static final String ENDPOINT_TEXT_CONTENT = "api/funny_text.json.php";
    public static final String ENDPOINT_DYNAMIC_CONTENT = "api/dynamic_content.json.php";

    // App Configuration
    public static final String APP_NAME = "Text Repeater";
    public static final String APP_PACKAGE = "com.anginatech.textrepeater";
    public static final String APP_VERSION = "3.9.1";
    public static final int APP_VERSION_CODE = 3;

    // AdMob Configuration Keys
    public static final String PREF_ADMOB_ENABLED = "admob_enabled";
    public static final String PREF_BANNER_AD_ID = "banner_ad_id";
    public static final String PREF_INTERSTITIAL_AD_ID = "interstitial_ad_id";
    public static final String PREF_APP_OPEN_AD_ID = "app_open_ad_id";
    public static final String PREF_AD_FREQUENCY = "ad_frequency_minutes";
    public static final String PREF_MAX_ADS_PER_SESSION = "max_ads_per_session";
    public static final String PREF_TEST_MODE = "test_mode";

    // App Settings Keys
    public static final String PREF_MAINTENANCE_MODE = "maintenance_mode";
    public static final String PREF_FORCE_UPDATE = "force_update";
    public static final String PREF_MIN_VERSION = "min_supported_version";
    public static final String PREF_LATEST_VERSION = "latest_version";
    public static final String PREF_UPDATE_MESSAGE = "update_message";
    public static final String PREF_DOWNLOAD_URL = "download_url";

    // SharedPreferences Names
    public static final String PREFS_APP_CONFIG = "app_config";
    public static final String PREFS_ADMOB_CONFIG = "admob_config";
    public static final String PREFS_USER_DATA = "user_data";
    public static final String PREFS_APP_SETTINGS = "MyAppPrefs";
    public static final String PREFS_NIGHT_MODE = "nightModePrefs";

    // Default Values
    public static final boolean DEFAULT_ADMOB_ENABLED = true;
    public static final int DEFAULT_AD_FREQUENCY = 4; // minutes
    public static final int DEFAULT_MAX_ADS_PER_SESSION = 3;
    public static final boolean DEFAULT_TEST_MODE = false;
    public static final boolean DEFAULT_MAINTENANCE_MODE = false;
    public static final boolean DEFAULT_FORCE_UPDATE = false;

    // Network Configuration
    public static final int CACHE_DURATION_CONFIG = 600; // 10 minutes
    public static final int CACHE_DURATION_ADS = 300; // 5 minutes
    public static final int CACHE_DURATION_SETTINGS = 300; // 5 minutes

    // App Open Ad Configuration
    public static final long MIN_INTERVAL_BETWEEN_ADS = 4 * 60 * 1000; // 4 minutes
    public static final int MAX_ADS_PER_SESSION = 3;
    public static final long AD_EXPIRY_TIME = 4 * 60 * 60 * 1000; // 4 hours

    // Splash Screen Configuration
    public static final int SPLASH_TIMEOUT = 1000; // 1 second
    public static final int SPLASH_AD_TIMEOUT = 20000; // 20 seconds max for ad loading (increased for better ad loading)
    public static final int MAX_SPLASH_TIMEOUT = 25000; // 25 seconds absolute maximum timeout

    // Logging
    public static final String LOG_TAG = "TextRepeater";
    public static final boolean DEBUG_MODE = true; // Set to false in production

    /**
     * Build complete API URL with endpoint (for legacy Volley requests)
     */
    public static String buildApiUrl(String endpoint) {
        return LEGACY_API_URL + "?path=" + endpoint;
    }

    /**
     * Build modern API URL for Retrofit (without index.php)
     */
    public static String buildModernApiUrl(String endpoint) {
        return BASE_URL + "index.php?path=" + endpoint;
    }

    /**
     * Build direct API URL (for endpoints that don't use the app-api router)
     */
    public static String buildDirectApiUrl(String endpoint) {
        return BASE_DOMAIN + "/" + endpoint;
    }

    /**
     * Get API URL for specific endpoint with parameters
     */
    public static String buildApiUrl(String endpoint, String... params) {
        StringBuilder url = new StringBuilder(buildApiUrl(endpoint));

        if (params.length > 0 && params.length % 2 == 0) {
            url.append("&");
            for (int i = 0; i < params.length; i += 2) {
                if (i > 0) {
                    url.append("&");
                }
                url.append(params[i]).append("=").append(params[i + 1]);
            }
        }

        return url.toString();
    }

    /**
     * Check if debug mode is enabled
     */
    public static boolean isDebugMode() {
        return DEBUG_MODE;
    }

    /**
     * Get app version info
     */
    public static String getAppVersionInfo() {
        return APP_NAME + " v" + APP_VERSION + " (" + APP_VERSION_CODE + ")";
    }
}
