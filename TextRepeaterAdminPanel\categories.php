<?php
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/TextContentManager.php';

$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$textManager = new TextContentManager();
$user = $auth->getCurrentUser();

// Handle actions
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'delete':
            $id = $_POST['id'] ?? 0;
            if ($id) {
                $result = $textManager->deleteCategory($id);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'error';
            }
            break;
            
        case 'toggle_status':
            $id = $_POST['id'] ?? 0;
            if ($id) {
                $result = $textManager->toggleCategoryStatus($id);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'error';
            }
            break;
    }
}

// Get categories
$categories = $textManager->getCategories();

// Get content count for each category
$category_stats = [];
foreach ($categories as $category) {
    $category_stats[$category['id']] = $textManager->getTextContentCount($category['id']);
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Category Management</h3>
                    <div>
                        <a href="category-form.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Category
                        </a>
                        <a href="text-content.php" class="btn btn-secondary">
                            <i class="fas fa-file-text"></i> Manage Content
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo count($categories); ?></h4>
                                            <p class="mb-0">Total Categories</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-tags fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo count(array_filter($categories, function($cat) { return $cat['is_active']; })); ?></h4>
                                            <p class="mb-0">Active Categories</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo array_sum($category_stats); ?></h4>
                                            <p class="mb-0">Total Content</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-file-text fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?php echo count(array_filter($categories, function($cat) { return !$cat['is_active']; })); ?></h4>
                                            <p class="mb-0">Inactive Categories</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-pause-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Categories Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Display Name</th>
                                    <th>Description</th>
                                    <th>Content Count</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($categories)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center">No categories found</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($categories as $category): ?>
                                        <tr>
                                            <td><?php echo $category['id']; ?></td>
                                            <td>
                                                <code><?php echo htmlspecialchars($category['name']); ?></code>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($category['display_name']); ?></strong>
                                            </td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                                    <?php echo htmlspecialchars($category['description'] ?: 'No description'); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $category_stats[$category['id']]; ?> messages
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $category['is_active'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $category['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $category['sort_order']; ?></td>
                                            <td><?php echo date('M j, Y', strtotime($category['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="category-form.php?id=<?php echo $category['id']; ?>"
                                                       class="btn btn-outline-primary btn-sm" title="Edit Category">
                                                        <i class="fas fa-edit" aria-hidden="true"></i>
                                                    </a>

                                                    <a href="text-content.php?category=<?php echo $category['id']; ?>"
                                                       class="btn btn-outline-info btn-sm" title="View Content">
                                                        <i class="fas fa-eye" aria-hidden="true"></i>
                                                    </a>

                                                    <form method="POST" style="display: inline;"
                                                          onsubmit="return confirm('Toggle status for this category?')">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="id" value="<?php echo $category['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-warning btn-sm" title="Toggle Status">
                                                            <i class="fas fa-toggle-<?php echo $category['is_active'] ? 'on' : 'off'; ?>" aria-hidden="true"></i>
                                                        </button>
                                                    </form>
                                                    
                                                    <?php if ($category_stats[$category['id']] == 0): ?>
                                                        <form method="POST" style="display: inline;"
                                                              onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                                                            <input type="hidden" name="action" value="delete">
                                                            <input type="hidden" name="id" value="<?php echo $category['id']; ?>">
                                                            <button type="submit" class="btn btn-outline-danger btn-sm" title="Delete Category">
                                                                <i class="fas fa-trash-alt" aria-hidden="true"></i>
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <button class="btn btn-outline-secondary btn-sm" disabled title="Cannot delete category with content">
                                                            <i class="fas fa-lock" aria-hidden="true"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-4">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Category Management Tips:</h6>
                            <ul class="mb-0">
                                <li><strong>Name:</strong> Used in API calls (lowercase, no spaces recommended)</li>
                                <li><strong>Display Name:</strong> Shown in admin panel and forms</li>
                                <li><strong>Sort Order:</strong> Lower numbers appear first</li>
                                <li><strong>Delete:</strong> Only empty categories can be deleted</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
